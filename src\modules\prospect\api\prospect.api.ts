import { RequestState } from "@/common/configs/app.contants";
import { appRequest } from "@/common/configs/app.di-container";
import { getErrorMessage } from "@/utils/error.utils";
import { ProspectQuery } from "../interface/prospect.query";
import { ProspectBodyType } from "../schema/prospect.schema";

const prospectRequest = {
  getListProspect: async (params: ProspectQuery) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/Prospect/GetAll`,
        {
          ...params,
          projectIds: JSON.stringify(params.projectIds),
        }
      );
      return {
        state: RequestState.success,
        data: {
          items: response?.result?.items,
          pagination: {
            current:
              Math.floor(
                (params?.skipCount ?? 1) / (params?.maxResultCount ?? 10)
              ) + 1,
            pageSize: params?.maxResultCount ?? 10,
            total: response?.result?.totalCount,
          },
        },
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  create: async (body: ProspectBodyType) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/Prospect/Create`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  delete: async (params: { id: number }) => {
    try {
      const response = await appRequest.delete<any>(
        `/services/app/Prospect/Delete?Id=${params.id}`
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  update: async (body: ProspectBodyType) => {
    try {
      const response = await appRequest.put<any>(
        `/services/app/Prospect/Update`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  getDetail: async (id: string) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/Prospect/Get?Id=${id}`
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
};

export default prospectRequest;
